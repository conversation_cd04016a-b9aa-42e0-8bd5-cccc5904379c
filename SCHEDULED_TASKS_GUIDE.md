# GIPA API - Scheduled Tasks Guide

## Overview
Laravel 12-ში scheduled tasks (cron jobs) განისაზღვრება `routes/console.php` ფაილში. ეს არის ახალი მიდგომა Laravel 12-ში, სადაც ძველი `app/Console/Kernel.php` ფაილი აღარ გამოიყენება.

## Scheduled Tasks Configuration Location

### Laravel 12-ში (ახალი მიდგომა):
```php
// routes/console.php
use Illuminate\Support\Facades\Schedule;

Schedule::command('contracts:check-expirations')
    ->dailyAt('09:00')
    ->timezone('Asia/Tbilisi')
    ->onOneServer()
    ->emailOutputOnFailure(['<EMAIL>']);
```

### ძველ Laravel ვერსიებში (აღარ გამოიყენება):
```php
// app/Console/Kernel.php - ეს ფაილი წაშლილია Laravel 12-ში
protected function schedule(Schedule $schedule)
{
    $schedule->command('contracts:check-expirations')->dailyAt('09:00');
}
```

## Current Scheduled Tasks

### 1. Contract Expiration Check
- **Command**: `contracts:check-expirations`
- **Schedule**: ყოველდღე 09:00-ზე
- **Purpose**: ამოწმებს კონტრაქტებს რომლებიც 45 დღეში ამოიწურება
- **Timezone**: Asia/Tbilisi
- **Error Notification**: <EMAIL>

### 2. Finance Status Update
- **Command**: `finance:status-update`
- **Schedule**: ყოველდღე 02:00-ზე
- **Purpose**: განაახლებს სტუდენტების ფინანსურ სტატუსს
- **Timezone**: Asia/Tbilisi
- **Error Notification**: <EMAIL>

### 3. Block Students with Overdue Payments
- **Command**: `finance:graphic-block-student`
- **Schedule**: ყოველდღე 03:00-ზე
- **Purpose**: ბლოკავს სტუდენტებს დაგვიანებული გადახდებით
- **Timezone**: Asia/Tbilisi
- **Error Notification**: <EMAIL>

### 4. Activate Blocked Students
- **Command**: `finance:graphic-active-student`
- **Schedule**: ყოველდღე 04:00-ზე
- **Purpose**: აქტივაციას უკეთებს დაბლოკილ სტუდენტებს
- **Timezone**: Asia/Tbilisi
- **Error Notification**: <EMAIL>

### 5. Finance Report
- **Command**: `send:finance-report`
- **Schedule**: ყოველ ორშაბათს 08:00-ზე
- **Purpose**: აგზავნის ფინანსურ რეპორტს ადმინისტრაციისთვის
- **Timezone**: Asia/Tbilisi
- **Error Notification**: <EMAIL>

### 6. Survey Activation
- **Command**: `activation:survey`
- **Schedule**: ყოველდღე 01:00-ზე
- **Purpose**: აქტივაციას უკეთებს გამოკითხვებს
- **Timezone**: Asia/Tbilisi
- **Error Notification**: <EMAIL>

### 7. Log Cleanup
- **Type**: Closure function
- **Schedule**: ყოველ კვირას 01:00-ზე
- **Purpose**: ასუფთავებს ძველ log ფაილებს
- **Timezone**: Asia/Tbilisi

## Server Configuration

### Cron Entry
სერვერზე უნდა დაემატოს მხოლოდ ერთი cron entry:

```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### Laravel Cloud/Forge
თუ იყენებთ Laravel Cloud ან Forge-ს, scheduled tasks ავტომატურად მუშაობს.

## Management Commands

### View Scheduled Tasks
```bash
php artisan schedule:list
```

### Run Scheduler Manually
```bash
php artisan schedule:run
```

### Run Scheduler in Development
```bash
php artisan schedule:work
```

### Clear Schedule Cache
```bash
php artisan schedule:clear-cache
```

### Test Individual Commands
```bash
php artisan contracts:check-expirations
php artisan finance:status-update
php artisan finance:graphic-block-student
php artisan finance:graphic-active-student
php artisan send:finance-report
php artisan activation:survey
```

## Features Used

### 1. Timezone Support
ყველა task იყენებს `Asia/Tbilisi` timezone-ს:
```php
->timezone('Asia/Tbilisi')
```

### 2. Single Server Execution
თავიდან აცილებს task-ების დუბლირებას multiple servers-ზე:
```php
->onOneServer()
```

### 3. Error Notifications
ავტომატურად აგზავნის email-ს error-ების შემთხვევაში:
```php
->emailOutputOnFailure(['<EMAIL>'])
```

### 4. Frequency Options
- `->dailyAt('09:00')` - ყოველდღე კონკრეტულ დროს
- `->weeklyOn(1, '08:00')` - კვირაში ერთხელ (ორშაბათს)
- `->weeklyOn(0, '01:00')` - კვირაში ერთხელ (კვირას)

## Testing

### Automated Tests
```bash
php artisan test tests/Feature/ScheduledTasksTest.php
```

### Manual Testing
```bash
# ამოწმებს რომ ყველა command არსებობს
php artisan list | grep -E "(contracts|finance|activation)"

# ამოწმებს scheduled tasks-ს
php artisan schedule:list

# გაუშვებს scheduler-ს
php artisan schedule:run
```

## Troubleshooting

### Common Issues

1. **Commands Not Running**
   - შეამოწმეთ cron entry სერვერზე
   - შეამოწმეთ file permissions
   - შეამოწმეთ PHP path

2. **Timezone Issues**
   - დარწმუნდით რომ server timezone სწორია
   - შეამოწმეთ `config/app.php`-ში timezone

3. **Email Notifications Not Working**
   - შეამოწმეთ mail configuration
   - შეამოწმეთ SMTP settings

### Debugging
```bash
# ვრცელი ინფორმაცია
php artisan schedule:list -v

# Log ფაილების შემოწმება
tail -f storage/logs/laravel.log
tail -f storage/logs/cron_logs.log
```

## Migration from Laravel 9 to Laravel 12

### What Changed:
1. **Removed**: `app/Console/Kernel.php`
2. **Added**: Scheduling in `routes/console.php`
3. **New Features**: Better timezone support, improved error handling

### Migration Steps:
1. ძველი `schedule()` method-ის კოდი გადატანილია `routes/console.php`-ში
2. დამატებულია error notifications
3. დამატებულია timezone configuration
4. დამატებულია single server execution

## Best Practices

1. **Always use timezone** - განსაკუთრებით production environment-ში
2. **Use onOneServer()** - multiple servers-ის შემთხვევაში
3. **Add error notifications** - მნიშვნელოვანი tasks-ისთვის
4. **Test commands individually** - deployment-მდე
5. **Monitor logs** - რეგულარულად შეამოწმეთ execution logs

## Security Considerations

1. **Email addresses** - დარწმუნდით რომ notification emails უსაფრთხოა
2. **File permissions** - scheduler-ს უნდა ჰქონდეს საჭირო permissions
3. **Database access** - commands-ს უნდა ჰქონდეს database access
4. **Environment variables** - დარწმუნდით რომ .env ფაილი კარგად არის კონფიგურირებული
