<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Artisan;

class ScheduledTasksTest extends TestCase
{
    /**
     * Test that all scheduled tasks are properly configured.
     */
    public function test_scheduled_tasks_are_configured(): void
    {
        $schedule = app(Schedule::class);
        $events = $schedule->events();

        // Check that we have the expected number of scheduled tasks
        $this->assertGreaterThanOrEqual(6, count($events));

        // Check that specific commands are scheduled
        $commandNames = collect($events)->map(function ($event) {
            return $event->command ?? $event->description;
        })->toArray();

        // Check that commands contain the expected artisan commands
        $this->assertTrue(
            collect($commandNames)->contains(function ($command) {
                return str_contains($command, 'contracts:check-expirations');
            }),
            'contracts:check-expirations command should be scheduled'
        );

        $this->assertTrue(
            collect($commandNames)->contains(function ($command) {
                return str_contains($command, 'finance:status-update');
            }),
            'finance:status-update command should be scheduled'
        );

        $this->assertTrue(
            collect($commandNames)->contains(function ($command) {
                return str_contains($command, 'finance:graphic-block-student');
            }),
            'finance:graphic-block-student command should be scheduled'
        );

        $this->assertTrue(
            collect($commandNames)->contains(function ($command) {
                return str_contains($command, 'finance:graphic-active-student');
            }),
            'finance:graphic-active-student command should be scheduled'
        );

        $this->assertTrue(
            collect($commandNames)->contains(function ($command) {
                return str_contains($command, 'send:finance-report');
            }),
            'send:finance-report command should be scheduled'
        );

        $this->assertTrue(
            collect($commandNames)->contains(function ($command) {
                return str_contains($command, 'activation:survey');
            }),
            'activation:survey command should be scheduled'
        );

        $this->assertContains("cleanup-logs", $commandNames);
    }

    /**
     * Test that schedule:list command works.
     */
    public function test_schedule_list_command(): void
    {
        $this->artisan('schedule:list')
            ->assertExitCode(0);
    }

    /**
     * Test that individual scheduled commands exist and can be run.
     */
    public function test_scheduled_commands_exist(): void
    {
        // Test that commands exist (they should not fail with "command not found")
        $this->artisan('contracts:check-expirations --help')->assertExitCode(0);
        $this->artisan('finance:status-update --help')->assertExitCode(0);
        $this->artisan('finance:graphic-block-student --help')->assertExitCode(0);
        $this->artisan('finance:graphic-active-student --help')->assertExitCode(0);
        $this->artisan('send:finance-report --help')->assertExitCode(0);
        $this->artisan('activation:survey --help')->assertExitCode(0);
    }

    /**
     * Test that schedule:run command works without errors.
     */
    public function test_schedule_run_command(): void
    {
        // This will run the scheduler but won't execute tasks unless it's their time
        $this->artisan('schedule:run')
            ->assertExitCode(0);
    }

    /**
     * Test timezone configuration.
     */
    public function test_schedule_timezone_configuration(): void
    {
        $schedule = app(Schedule::class);
        $events = $schedule->events();

        // Check that events have timezone set
        foreach ($events as $event) {
            if (method_exists($event, 'timezone')) {
                // Most of our tasks should have Asia/Tbilisi timezone
                $this->assertTrue(
                    $event->timezone === null || $event->timezone === 'Asia/Tbilisi',
                    'Task should have Asia/Tbilisi timezone or default timezone'
                );
            }
        }
    }
}
