<?php

namespace App\Exports;

use App\Models\Finance\Finance;
use App\Models\Finance\FinanceCalendar;
use App\Models\Finance\FinanceScheduler;
use App\Models\Reestry\Student\Student;
use App\Models\Setting;
use Carbon\Carbon;
use DateTime;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class StudentFinanceExport implements FromCollection, WithHeadings, ShouldAutoSize, WithColumnFormatting
{
    public function __construct()
    {
        $dates = $this->generateAcademicYearDates(now()->year-1);
        $learnYear = Setting::where('key', 'learn_year')->pluck('value')->first();
        $nextYear = $learnYear + 1;

        $this->student = Student::query()
            ->with('activeSchedule.calendars')
            ->get()
            ->map(function ($student, $key) use ($dates, $learnYear, $nextYear) {
                $data = [
                    'N' => $key + 1,
                    'სახელი გვარი' => $student->name . ' ' . $student->surname,
                    'პირადი ნომერი' => '="' . str_replace(' ', '', $student->personal_id) . '"',
                    'სკოლა' => $student?->program?->school?->name_ka ?? ' - ',
                    'პროგრამა' => $student?->program?->name_ka ?? ' - ',
                    'სტატუსი' => $student?->status?->name_ka ?? ' - ',
                ];
                foreach ($dates as $date) {
                    $data[$date] = ' - ';
                }
                $data['ჯამი'] = '0';


                // ind grafiki
                $callendars = $student?->activeSchedule?->calendars ?? null;
                if($callendars)
                {
                    foreach ($callendars as $calendar) {
                        $amountDate = Carbon::createFromFormat('d/m/Y', $calendar['start_date'])->format('d-m-Y');
                        $data[$amountDate] = $calendar['amount'];
                        $data['ჯამი'] += $calendar['amount'];
                    }

                    return $data;
                } //



                // sheidzleba gabagos
                $standardData = Finance::where('piradi_nom', $student->personal_id)
                    ->where('saswavlo_weli', $learnYear)
                    ->where('ProgramID', $student->program_id)
                    ->whereIn('Qrter', [1,2,3,4])
                    ->get()
                ;
                foreach ($standardData as $standard) {
                    if ($standard->Qrter == 1)
                    {
                        $paymentAmount = $standard?->kontraqtis_tanxa + $standard?->dam_sagnebi + $standard?->kvartlis_nashti ?? 0;
                        $standardDate = "01-10-$learnYear"; // or "01/09/{$currentYear}" end_date
                    }
                    elseif($standard->Qrter == 2)
                    {
                        $paymentAmount = $standard?->kontraqtis_tanxa + $standard?->kvartlis_nashti ?? 0;
                        $standardDate = "01-12-$learnYear"; // or "02/10/{$currentYear}" end_date
                    }
                    elseif($standard->Qrter == 3)
                    {
                        $paymentAmount = $standard?->kontraqtis_tanxa  + $standard?->dam_sagnebi + $standard?->kvartlis_nashti ?? 0;
                        $standardDate = "01-03-$nextYear"; // or "02/12/{$currentYear}" end_date
                    }
                    elseif($standard->Qrter == 4)
                    {
                        $paymentAmount = $standard?->kontraqtis_tanxa + $standard?->kvartlis_nashti ?? 0;
                        $standardDate = "01-06-$nextYear"; // or "02/03/{$nextYear}" end_date
                    }

                    $data[$standardDate] = $paymentAmount;
                    $data['ჯამი'] += $paymentAmount;
                }

                return $data;
            })
        ;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->student->values();
    }

    public function headings(): array
    {
        return array_keys($this?->student?->first()) ?? [];
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_TEXT, // მესამე სვეტი: პირადი ნომერი
        ];
    }

    /**
     * აგენერირებს სასწავლო წლის ყოველ დღეს.
     *
     * @param int $year  - ის წელი, რომლითაც იწყება სასწავლო წელი (ანუ 2024 -> დაიწყებს 01.09.2024 და დაამთავრებს 31.08.2025)
     * @return \Illuminate\Support\Collection
     */
    function generateAcademicYearDates(int $year): \Illuminate\Support\Collection
    {
        $startDate = Carbon::createFromFormat('d-m-Y', "01-09-{$year}");
        $endDate = $startDate->copy()->addYear()->subDay(); // 31 აგვისტო შემდეგი წლის

        $dates = collect();
        $current = $startDate->copy();
        while ($current->lte($endDate)) {
            $dates->push($current->format('d-m-Y'));
            $current->addDay();
        }

        return $dates;
    }

}
