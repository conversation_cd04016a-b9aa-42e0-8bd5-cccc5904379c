<?php

namespace App\Services;

use App\Filters\ProgramFilter;
use App\Filters\SchoolFilter;
use App\Models\FinanceLog;
use App\Models\Finance\Finance;
use App\Models\Finance\FinanceCalendar;
use App\Models\Finance\FinanceScheduler;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Setting;
use App\Models\User\UserType;
use DB;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use phpDocumentor\Reflection\Types\Null_;
use Symfony\Component\HttpFoundation\Response as ResponseCode;
ini_set('memory_limit', '512M');

class FinanceService
{

    public ?int $userId;
    public ?int $financeId;

    private $learn_year;

    public function parseQuarter($periods)
    {
        $periods = explode(',', $periods);
        [$from, $to] = $periods;
        [$fromDay, $fromMonth, $fromYear] = explode('/', $from);
        [$toDay, $toMonth, $toYear] = explode('/', $to);
        $quarters = collect([]);

        if ($fromMonth < 9) {
            $fromYear--;
        }

        $quarterFrom = (new FinanceService())->getQuarter($fromYear, $fromMonth, $fromDay);
        $quarterTo = (new FinanceService())->getQuarter($toYear, $toMonth, $toDay);

        foreach (range($fromYear, $toYear) as $year) {
            if ($fromYear !== $toYear) {
                if ($fromYear == $year) {
                    $quarterTo = 4;

                    if ($fromYear + 1 == $toYear && intval($toMonth) < 9) {
                        $quarterTo = (new FinanceService())->getQuarter($year + 1, $toMonth, $toDay);
                    }

                    foreach (range($quarterFrom, $quarterTo) as $quarter) {
                        $quarters->push("$year-$quarter");
                    }
                } else if (
                    $toYear == $year &&
                    Carbon::createFromDate($toYear, $toMonth, $toDay) > Carbon::createFromDate($toYear, '08', '31')
                ) {
                    $quarterFrom = 1;
                    $quarterTo = (new FinanceService())->getQuarter($year, $toMonth, $toDay);

                    foreach (range($quarterFrom, $quarterTo) as $quarter) {
                        $quarters->push("$year-$quarter");
                    }
                } elseif ($toYear != $year) {
                    $quarterTo = 4;
                    $quarterFrom = 1;

                    if ($year + 1 == $toYear && intval($toMonth) < 9) {
                        $quarterTo = (new FinanceService())->getQuarter($year + 1, $toMonth, $toDay);
                    }

                    foreach (range($quarterFrom, $quarterTo) as $quarter) {
                        $quarters->push("$year-$quarter");
                    }
                }
            } else {
                foreach (range($quarterFrom, $quarterTo) as $quarter) {
                    if ($quarter >= 3 && ($fromMonth != 12 || $toMonth != 12)) {
                        $updatedYear = $year - 1;
                        $quarters->push("$updatedYear-$quarter");
                    } else {
                        $quarters->push("$year-$quarter");
                    }
                }
            }
        }

        return $quarters;
    }

    public function __construct($userId = null, $financeId = null)
    {
        $this->userId = $userId;
        $this->financeId = $financeId;
        $this->learn_year = Setting::where('key', 'learn_year')->pluck('value')->first();
    }

    public function debtQuarter($filter)
    {
        $finances = Finance::filter($filter)
            ->whereNotNull('Qrter')
//            ->whereNotNull('kontraqtis_tanxa')
//            ->where('piradi_nom', '01005027326')
            ->with(['program',
                'student' => function ($query) {
                    $query->select('id', 'name', 'surname', 'personal_id', 'program_id', 'user_id', 'status_id', 'school_id');
                }
            ])
            ->join('students', 'finance_new.piradi_nom', '=', 'students.personal_id')
            ->orderBy('max_student_surname')
            ->select([
                'finance_new.piradi_nom',
                DB::raw('MAX(students.surname) as max_student_surname'),
                DB::raw('ROUND(SUM(kontraqtis_tanxa)) as current_kontraqtis_tanxa'),
                DB::raw('MAX(saswavlo_weli) as max_saswavlo_weli'),
                DB::raw('MIN(saswavlo_weli) as min_saswavlo_weli'),
                DB::raw('ROUND(SUM(dam_sagnebi), 2) as total_dam_sagnebi'),
                DB::raw('ROUND(SUM(sareitingo_fasdakleba),2) as total_sareitingo_fasdakleba'), //სარეიტინგო ფასდაკლება +
                DB::raw('ROUND(SUM(grantianis_fasdakleba),2) as total_grantianis_fasdakleba'),// სარეიტინგო ფასდაკლება +
                DB::raw('ROUND(SUM(extra),2) as total_extra'),
                DB::raw('ROUND(SUM(sax_daxmareba),2) as total_sax_daxmareba'), // სოც. დახმარება
                DB::raw('ROUND(SUM(sax_granti),2) as total_sax_granti'), // გრანტი
                DB::raw('ROUND(SUM(meriis_daxmareba),2) as total_meriis_daxmareba'), // სხვა. დახმარება
                DB::raw('ROUND(SUM(charicxuli_studenti),2) as total_charicxuli_studenti'), // მიღებული
                DB::raw('ROUND(SUM(charicxuli_granti),2) as total_charicxuli_granti'),
                DB::raw('ROUND(SUM(charicxuli_sax_daxmareba),2) as total_charicxuli_sax_daxmareba'),
                DB::raw('ROUND(SUM(charicxuli_meriis_daxmareba),2) as total_charicxuli_meriis_daxmareba'),
                DB::raw('ROUND(SUM(akademiuris_tanxa1), 2) as total_akademiuris_tanxa1'),
//                DB::raw('MAX(kvartlis_jami) as last_kvartlis_jami'), // სტანდ ტოტალ პრაის
//                DB::raw('FIRST_VALUE(kvartlis_jami) OVER (PARTITION BY kvartlis_jami ORDER BY desc) AS last_kvartlis_jami'), // სტანდ ტოტალ პრაის
//                DB::raw('(SELECT kvartlis_jami FROM students WHERE personal_id = finance_new.piradi_nom ORDER BY created_at DESC LIMIT 1) as last_kvartlis_jami'),
//                DB::raw('(SELECT ROUND(kvartlis_jami,2)
//                  FROM finance_new fn
//                  WHERE fn.piradi_nom = finance_new.piradi_nom
//                  ORDER BY fn.id DESC
//                  LIMIT 1) AS last_kvartlis_jami'),
                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(CAST(kvartlis_nashti AS CHAR) ORDER BY finance_new.id), \',\', 1) AS last_kvartlis_nashti'), // first
//                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(CAST(kvartlis_jami AS CHAR) ORDER BY finance_new.id), \',\', -1) AS last_kvartlis_jami'),
//                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(CAST(kvartlis_jami AS CHAR) ORDER BY finance_new.id DESC), \',\', 1) AS last_kvartlis_jami'),
//                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(CAST(kvartlis_jami AS CHAR) ORDER BY finance_new.id DESC), \',\', -1) AS last_kvartlis_jami'),
                DB::raw('(SELECT kvartlis_jami
              FROM finance_new AS f
              WHERE f.piradi_nom = finance_new.piradi_nom
              ORDER BY f.id DESC) AS last_kvartlis_jami'),
//                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(CAST(kvartlis_jami AS CHAR) ORDER BY finance_new.id), \',\', 1) AS last_kvartlis_jami'), // first
//                DB::raw('MIN(kvartlis_nashti) as last_kvartlis_nashti'), // ძველი დავალიანება
                DB::raw('ROUND((SUM(kontraqtis_tanxa) + SUM(dam_sagnebi))) as total_price'), // ჯამი
            ])
            ->addSelect(DB::raw('ROUND((SUM(kontraqtis_tanxa)
                    + SUM(dam_sagnebi)
                    - SUM(akademiuris_tanxa1)
                    - SUM(sax_granti)
                    - SUM(sareitingo_fasdakleba)
                    - SUM(grantianis_fasdakleba)
                    - SUM(extra))
                    - SUM(sax_daxmareba)
                    - SUM(meriis_daxmareba), 2)
                as student_total_price')) // სტუდენტი
            ->groupBy('piradi_nom')
            ->get()
        ;

        $periods = explode(',', $filter->filters()['periods'])[1];
        $dateTo = Carbon::createFromFormat('d/m/Y', $periods);

        $finances = $finances->map(function ($finance) use($dateTo, $filter){
            $finance->quarter_debt_total = null;
            $finance->total_kontraqtis_tanxa = round((float)
                Finance::where('piradi_nom', $finance->piradi_nom)
                ->whereBetween('saswavlo_weli', [$finance->min_saswavlo_weli, $finance->max_saswavlo_weli])
                ->whereIn('ProgramId',
                    $finance
                        ->where('piradi_nom', $finance->piradi_nom)
                        ->filter($filter)
                        ->get()
                        ->pluck('ProgramID')
                )
                ->sum('kontraqtis_tanxa')
            ,2);

            if ($finance->student?->financeScheduler)
            {
                $financeCalendarDebt = FinanceCalendar::whereFinanceSchedulerId($finance->student->financeScheduler->id)
                    ->where('end_date', '<=', $dateTo)
                    ->sum('amount')
                ;
                $financeStartLearnYear = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();

                $payed = FinanceLog::where('piradi_nom', $finance->piradi_nom)
                    ->whereBetween('tarigi', [Carbon::createFromFormat('d/m/Y', $financeStartLearnYear), $dateTo])
                    ->sum('tanxa');

                $startDate = Carbon::createFromFormat('d/m/Y', "05/06/" . $dateTo->year); // 4 ივნისი
                $endDate = Carbon::createFromFormat('d/m/Y', "31/08/" . $dateTo->year);   // 4 აგვისტო

                if ($dateTo->between($startDate, $endDate))
                {
                    $finance->quarter_debt_total = max(0, round($finance->student_total_price - $payed, 2)); // ჯამური დავალიანება როდესაც 4 ივნისზე მეტია
                }
                else
                {
                    $finance->quarter_debt_total = max(0, round($financeCalendarDebt - $payed, 2)); // ჯამური დავალიანება
                }

            }

            return $finance;
        });

        return $finances;
    }

    public function debtQuarterTest($filter, $export=false)
    {
//        $tess  = Finance::query()->pluck('piradi_nom');
//        $students = Student::query()->where('');
        $quarters = $this->parseQuarter($filter->filters()['periods'])->toArray();

        $finances = Finance::filter($filter)
            ->whereNotNull('Qrter')
//            ->where('piradi_nom', '01724094146')
            ->with(['program',
                'student' => function ($query) {
                    $query->select('id', 'name', 'surname', 'personal_id', 'program_id', 'user_id', 'status_id', 'school_id');
                }
            ])
            ->whereRaw('finance_new.ProgramID = (SELECT program_id FROM students WHERE students.personal_id = finance_new.piradi_nom LIMIT 1)')
            ->select([
                'finance_new.piradi_nom',
                DB::raw('(
                    SELECT MAX(surname)
                    FROM students
                    WHERE students.personal_id = finance_new.piradi_nom
                    AND students.program_id = finance_new.ProgramID
                ) as max_student_surname'), // Subquery for surname
                DB::raw('ROUND(SUM(kontraqtis_tanxa)) as current_kontraqtis_tanxa'),
                DB::raw('MAX(saswavlo_weli) as max_saswavlo_weli'),
                DB::raw('MIN(saswavlo_weli) as min_saswavlo_weli'),
                DB::raw('ROUND(SUM(dam_sagnebi), 2) as total_dam_sagnebi'),
                DB::raw('ROUND(SUM(sareitingo_fasdakleba), 2) as total_sareitingo_fasdakleba'), // სარეიტინგო ფასდაკლება +
                DB::raw('ROUND(SUM(grantianis_fasdakleba), 2) as total_grantianis_fasdakleba'), // სარეიტინგო ფასდაკლება +
                DB::raw('ROUND(SUM(extra), 2) as total_extra'),
                DB::raw('ROUND(SUM(sax_daxmareba), 2) as total_sax_daxmareba'), // სოც. დახმარება
                DB::raw('ROUND(SUM(sax_granti), 2) as total_sax_granti'), // გრანტი
                DB::raw('ROUND(SUM(meriis_daxmareba), 2) as total_meriis_daxmareba'), // სხვა. დახმარება
                DB::raw('ROUND(SUM(charicxuli_studenti), 2) as total_charicxuli_studenti'), // მიღებული
                DB::raw('ROUND(SUM(charicxuli_granti), 2) as total_charicxuli_granti'),
                DB::raw('ROUND(SUM(charicxuli_sax_daxmareba), 2) as total_charicxuli_sax_daxmareba'),
                DB::raw('ROUND(SUM(charicxuli_meriis_daxmareba), 2) as total_charicxuli_meriis_daxmareba'),
                DB::raw('ROUND(SUM(akademiuris_tanxa1), 2) as total_akademiuris_tanxa1'),
                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(CAST(kvartlis_nashti AS CHAR) ORDER BY finance_new.id), \',\', 1) AS last_kvartlis_nashti'), // first
                DB::raw('ROUND((SUM(kontraqtis_tanxa) + SUM(dam_sagnebi))) as total_price'), // ჯამი
            ])
            ->addSelect([
                DB::raw('(
                    SELECT ROUND(fn.kvartlis_jami, 2)
                    FROM finance_new fn
                    WHERE fn.piradi_nom = finance_new.piradi_nom
                    AND fn.ProgramID = finance_new.ProgramID
                    AND fn.PeriodFrom IN (' . implode(',', array_map(fn($q) => "'{$q}'", $quarters)) . ')
                    ORDER BY fn.id DESC
                    LIMIT 1
                ) AS last_kvartlis_jami'), // Simplified subquery without join
            ])
            ->addSelect(DB::raw('ROUND((
                  SUM(kontraqtis_tanxa)
                + SUM(dam_sagnebi)
                - SUM(akademiuris_tanxa1)
                - SUM(sax_granti)
                - SUM(sareitingo_fasdakleba)
                - SUM(grantianis_fasdakleba)
                - SUM(extra)
                - SUM(sax_daxmareba)
                - SUM(meriis_daxmareba)), 2) as student_total_price')) // სტუდენტი
            ->groupBy('finance_new.piradi_nom', 'finance_new.ProgramID') // Group by both to avoid duplicates
            ->orderByRaw('max_student_surname') // Sort by the subquery result
            ->get();

        $periods = explode(',', $filter->filters()['periods'])[1];
        $dateTo = Carbon::createFromFormat('d/m/Y', $periods);

        $finances = $finances->map(function ($finance) use($export, $dateTo, $filter){
            $finance->quarter_debt_total = null;
            $programId = Student::query()->where('personal_id', $finance->piradi_nom)->orderByDesc('id')->first()->program_id ?? null;
            $finance->total_kontraqtis_tanxa = round((float)
            Finance::where('piradi_nom', $finance->piradi_nom)
                ->where('ProgramID', $programId)
                ->whereBetween('saswavlo_weli', [$finance->min_saswavlo_weli, $finance->max_saswavlo_weli])
                ->whereIn('ProgramId',
                    $finance
                        ->where('piradi_nom', $finance->piradi_nom)
                        ->filter($filter)
                        ->get()
                        ->pluck('ProgramID')
                )
                ->sum('kontraqtis_tanxa')
                ,2);

            if ($finance->student?->financeScheduler)
            {
                $financeCalendarDebt = FinanceCalendar::whereFinanceSchedulerId($finance->student->financeScheduler->id)
                    ->where('end_date', '<=', $dateTo)
                    ->sum('amount')
                ;
                $financeStartLearnYear = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();

                $payed = FinanceLog::where('piradi_nom', $finance->piradi_nom)
                    ->whereBetween('tarigi', [Carbon::createFromFormat('d/m/Y', $financeStartLearnYear), $dateTo])
                    ->sum('tanxa');

                $startDate = Carbon::createFromFormat('d/m/Y', "05/06/" . $dateTo->year); // 4 ივნისი
                $endDate = Carbon::createFromFormat('d/m/Y', "31/08/" . $dateTo->year);   // 4 აგვისტო

                if ($dateTo->between($startDate, $endDate))
                {
                    $finance->quarter_debt_total = $export
                        ? round(($finance->student_total_price + $finance->last_kvartlis_nashti) - $payed, 2)
                        : max(0, round(($finance->student_total_price + $finance->last_kvartlis_nashti) - $payed, 2)); // ჯამური დავალიანება როდესაც 4 ივნისზე მეტია
                }
                else
                {
                    $finance->quarter_debt_total = $export
                        ? round($financeCalendarDebt - $payed, 2)
                        : max(0, round($financeCalendarDebt - $payed, 2)); // ჯამური დავალიანება
                }

            }

            return $finance;
        });

        return $finances;
    }

    /**
     * Get filtered finance data based on academic degree and training status
     */
    public function getFilteredFinanceData($filter, $academicDegreeIds, $isTrainings, $export = false)
    {
        $quarters = $this->parseQuarter($filter->filters()['periods'])->toArray();
        $periods = explode(',', $filter->filters()['periods'])[1];
        $dateTo = Carbon::createFromFormat('d/m/Y', $periods);

        $finances = Finance::filter($filter)
            ->whereNotNull('Qrter')
            ->where('IsTrainings', $isTrainings)
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                      ->from('students')
                      ->whereRaw('students.personal_id = finance_new.piradi_nom')
                      ->whereRaw('students.program_id = finance_new.ProgramID');
            })
            ->whereHas('program', function ($query) use ($academicDegreeIds) {
                $query->whereIn('academic_degree_id', $academicDegreeIds);
            })
            ->select([
                'finance_new.piradi_nom',
                'finance_new.ProgramID',
                DB::raw('(
                    SELECT MAX(surname)
                    FROM students
                    WHERE students.personal_id = finance_new.piradi_nom
                    AND students.program_id = finance_new.ProgramID
                ) as max_student_surname'),
                DB::raw('(
                    SELECT MAX(name)
                    FROM students
                    WHERE students.personal_id = finance_new.piradi_nom
                    AND students.program_id = finance_new.ProgramID
                ) as student_name'),
                DB::raw('(
                    SELECT p.name_ka
                    FROM programs p
                    WHERE p.id = finance_new.ProgramID
                    LIMIT 1
                ) as historical_program_name'),
                DB::raw('(
                    SELECT s.name_ka
                    FROM programs p
                    JOIN schools s ON s.id = p.school_id
                    WHERE p.id = finance_new.ProgramID
                    LIMIT 1
                ) as historical_school_name'),
                DB::raw('(
                    SELECT st.name_ka
                    FROM students stu
                    JOIN student_status_lists st ON st.id = stu.status_id
                    WHERE stu.personal_id = finance_new.piradi_nom
                    AND stu.program_id = finance_new.ProgramID
                    ORDER BY stu.id DESC
                    LIMIT 1
                ) as student_status_name'),
                DB::raw('ROUND(SUM(kontraqtis_tanxa)) as current_kontraqtis_tanxa'),
                DB::raw('MAX(saswavlo_weli) as max_saswavlo_weli'),
                DB::raw('MIN(saswavlo_weli) as min_saswavlo_weli'),
                DB::raw('ROUND(SUM(dam_sagnebi), 2) as total_dam_sagnebi'),
                DB::raw('ROUND(SUM(sareitingo_fasdakleba), 2) as total_sareitingo_fasdakleba'),
                DB::raw('ROUND(SUM(grantianis_fasdakleba), 2) as total_grantianis_fasdakleba'),
                DB::raw('ROUND(SUM(extra), 2) as total_extra'),
                DB::raw('ROUND(SUM(akademiuris_tanxa1), 2) as total_akademiuris_tanxa1'),
                DB::raw('ROUND(SUM(sax_granti), 2) as total_sax_granti'),
                DB::raw('ROUND(SUM(sax_daxmareba), 2) as total_sax_daxmareba'),
                DB::raw('ROUND(SUM(meriis_daxmareba), 2) as total_meriis_daxmareba'),
                DB::raw('ROUND(SUM(charicxuli_studenti), 2) as total_charicxuli_studenti'),
                DB::raw('ROUND(SUM(charicxuli_granti), 2) as total_charicxuli_granti'),
                DB::raw('ROUND(SUM(charicxuli_sax_daxmareba), 2) as total_charicxuli_sax_daxmareba'),
                DB::raw('ROUND(SUM(charicxuli_meriis_daxmareba), 2) as total_charicxuli_meriis_daxmareba'),
                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(CAST(kvartlis_nashti AS CHAR) ORDER BY finance_new.id), \',\', 1) AS last_kvartlis_nashti'),
                DB::raw('ROUND((SUM(kontraqtis_tanxa) + SUM(dam_sagnebi))) as total_price'),
            ])
            ->addSelect([
                DB::raw('(
                    SELECT ROUND(fn.kvartlis_jami, 2)
                    FROM finance_new fn
                    WHERE fn.piradi_nom = finance_new.piradi_nom
                    AND fn.ProgramID = finance_new.ProgramID
                    AND fn.PeriodFrom IN (' . implode(',', array_map(fn($q) => "'{$q}'", $quarters)) . ')
                    ORDER BY fn.id DESC
                    LIMIT 1
                ) AS last_kvartlis_jami'),
            ])
            ->addSelect(DB::raw('ROUND((
                  SUM(kontraqtis_tanxa)
                + SUM(dam_sagnebi)
                - SUM(akademiuris_tanxa1)
                - SUM(sax_granti)
                - SUM(sareitingo_fasdakleba)
                - SUM(grantianis_fasdakleba)
                - SUM(extra)
                - SUM(sax_daxmareba)
                - SUM(meriis_daxmareba)), 2) as student_total_price'))
            ->groupBy('finance_new.piradi_nom', 'finance_new.ProgramID')
            ->orderByRaw('max_student_surname')
            ->with(['student' => function ($query) {
                $query->select('id', 'name', 'surname', 'personal_id', 'program_id', 'user_id', 'status_id', 'school_id')
                      ->with('financeScheduler.calendars');
            }])
            ->get();

        // Apply the same processing logic as debtQuarterTest
        $finances = $finances->map(function ($finance) use($export, $dateTo, $filter){
            $finance->quarter_debt_total = null;
            // Use the specific ProgramID from this finance record, not the current student program
            $finance->total_kontraqtis_tanxa = round((float)
            Finance::where('piradi_nom', $finance->piradi_nom)
                ->where('ProgramID', $finance->ProgramID) // Use the historical ProgramID
                ->whereBetween('saswavlo_weli', [$finance->min_saswavlo_weli, $finance->max_saswavlo_weli])
                ->sum('kontraqtis_tanxa')
                ,2);

            // Get the student record for this specific program
            $historicalStudent = Student::where('personal_id', $finance->piradi_nom)
                ->where('program_id', $finance->ProgramID)
                ->first();

            if ($historicalStudent?->financeScheduler)
            {
                $financeCalendarDebt = FinanceCalendar::whereFinanceSchedulerId($historicalStudent->financeScheduler->id)
                    ->where('end_date', '<=', $dateTo)
                    ->sum('amount')
                ;
                $financeStartLearnYear = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();

                $payed = FinanceLog::where('piradi_nom', $finance->piradi_nom)
                    ->whereBetween('tarigi', [Carbon::createFromFormat('d/m/Y', $financeStartLearnYear), $dateTo])
                    ->sum('tanxa');

                $startDate = Carbon::createFromFormat('d/m/Y', "05/06/" . $dateTo->year);
                $endDate = Carbon::createFromFormat('d/m/Y', "31/08/" . $dateTo->year);

                if ($dateTo->between($startDate, $endDate))
                {
                    $finance->quarter_debt_total = $export
                        ? round(($finance->student_total_price + $finance->last_kvartlis_nashti) - $payed, 2)
                        : max(0, round(($finance->student_total_price + $finance->last_kvartlis_nashti) - $payed, 2));
                }
                else
                {
                    $finance->quarter_debt_total = $export
                        ? round($financeCalendarDebt - $payed, 2)
                        : max(0, round($financeCalendarDebt - $payed, 2));
                }

            }

            return $finance;
        });

        return $finances;
    }

    public function scheduler($calendarData)
    {
        if (!FinanceScheduler::query()
            ->where('user_id', $this->userId)
            ->where('status_id', '!=', 3) // rejected status
            ->where('created_at', '<=', (($this->learn_year+1).'-09-30'))
            ->active()
            ->exists()
        ){
            $isAdmin = auth()->user()->user_type_id === UserType::ADMINISTRATION;
            $scheduler = FinanceScheduler::query()->create([
                'user_id' => $this->userId,
                'is_active' => $isAdmin,
                'status_id' => $isAdmin ? 2 : 1,
            ]);
            if ($calendarData)
                foreach ($calendarData as $item) {
                    FinanceCalendar::create([
                        'start_date' => $item['start_date'],
                        'end_date' => $item['end_date'],
                        'amount' => $item['amount'],
                        'finance_scheduler_id' => $scheduler->id
                    ]);
                }
            return $scheduler->load(['calendars']);
        }
        return 'student already have scheduler';
    }

//    public function calendar(): Response
//    {
//        $financeScheduler = FinanceScheduler::withoutTrashed()
//            ->with(['calendars' => function ($query) {
//                $query->select('id', 'finance_scheduler_id', 'amount', DB::raw('DATE(start_date) as start_date'), DB::raw('DATE(end_date) as end_date'));
//            }])
//            ->whereUserId($this->userId)
//            ->whereIsActive(1)
//            ->first();
//
//        $amount = $this->currentTotalPrice(1);
//        $finance_learn_year_manual = Setting::where('key', 'finance_learn_year_manual')->pluck('value')->first();
//        $finance_learn_year_auto = Setting::where('key', 'finance_learn_year_auto')->pluck('value')->first();
//
//        if ($financeScheduler) {
//
//            $financeScheduler->calendars->transform(function ($calendar) {
//                //dd();
//                $calendar->start_date = $calendar->start_date->format('Y-m-d');
//                $calendar->end_date = $calendar->end_date->format('Y-m-d');
//                return $calendar;
//            });
//        }
//
//        return response([
//            'amount' => $amount,
//            'finance_learn_year_manual' => $finance_learn_year_manual,
//            'finance_learn_year_auto' => $finance_learn_year_auto,
//            'data' => $financeScheduler
//        ], ResponseCode::HTTP_OK);
//    }


    public function calendar(): Response
    {
        //ინდივიდუალური გრაფიკები
        $financeScheduler = FinanceScheduler::withoutTrashed()->with(['calendars'])
            ->whereUserId($this->userId)
            ->whereIsActive(1); // TODO:: სასწავლო წლის დაწყების წინ, ჯობმა გაასუფთავოს ყველა ჩანაწერი, გამოვიყენოთ softdelete

        $amount = $this->currentTotalPriceForStatement(1);
        $finance_learn_year_manual = Carbon::now()->format('d/m/Y').','.Setting::where('key', 'finance_learn_year_manual')->pluck('value')->first();
        $finance_learn_year_auto = Carbon::now()->format('d/m/Y').','.Setting::where('key', 'finance_learn_year_auto')->pluck('value')->first();
        if ($financeScheduler->exists()) {
            $response = $financeScheduler->first();
        } else {
            $response = array();
        }

        return response([
            'amount' => $amount,
            'finance_learn_year_manual' => $finance_learn_year_manual,
            'finance_learn_year_auto' => $finance_learn_year_auto,
            'data' => $response
        ], ResponseCode::HTTP_OK);
    }

    public function getStudentIdentityNumber(): string
    {
        return Student::whereUserId($this->userId)->first()->personal_id ?? 0;
    }

    public function getStudentProgramID(): int
    {
        return Student::whereUserId($this->userId)->first()->program_id ?? 0;
    }

    public function currentTotalPriceForStatement($finalDebt = null)
    {
        //$year = (now()->month >= 1 && now()->month <= 9) ? $this->learn_year - 1 : $this->learn_year;
       // $qrter = Finance::find($this->financeId)->Qrter; //TODO: ამის ამოღება რა საჭიროა? როდესაც ინდივიდუალური გრაფიკის გაკეთებას ვცდილობ მიერორებს რადგან ფაინანს აიდი არ მიეწოდება
        $studentFinance = Finance::whereSaswavloWeli($this->learn_year)
            ->wherePiradiNom($this->getStudentIdentityNumber())
            ->where('ProgramID', $this->getStudentProgramID())
            ->where('Qrter', 4)
            ->first();


////        $studentFinance = Finance::whereSaswavloWeli($this->learn_year)
////            ->wherePiradiNom($this->getStudentIdentityNumber())
////            ->where('ProgramID', $this->getStudentProgramID())
////            ->whereIn('Qrter', [1,2,3,4])
////            ->orderByDesc('id')->get();
////        $contractMoney = 0;
////        $additionalSubjects = 0;
////        $ratingDiscount = 0;
////        $grantStatusDiscount = 0;
////        $extraDiscount = 0;
////        $academicStatusDiscount = 0;
////        $stateDiscount = 0;
////        $cityHallDiscount = 0;
////        $grantDiscount = 0;
////        $depositAmount = 0;
////        foreach ($studentFinance as $item) {
////            $contractMoney += $item['kontraqtis_tanxa'];
////            $additionalSubjects += $item['dam_sagnebi'];
////            $ratingDiscount += $item['sareitingo_fasdakleba'];
////            $grantStatusDiscount += $item['grantianis_fasdakleba'];
////            $extraDiscount += $item['extra'];
////            $academicStatusDiscount += $item['akademiuris_tanxa1'];
////            $stateDiscount += $item['sax_daxmareba'];
////            $cityHallDiscount += $item['meriis_daxmareba'];
////            $grantDiscount += $item['sax_granti'];
////            $depositAmount += $item['charicxuli_studenti'];
////        }
////
////        if (isset($studentFinance)) {
////            $total = $contractMoney
////                + $additionalSubjects
////                - $academicStatusDiscount
////                - $grantDiscount
////                - $ratingDiscount
////                - $grantStatusDiscount
////                - $extraDiscount
////                - $stateDiscount
////                - $cityHallDiscount;
////
////        }
//        if ($finalDebt == 1) {
//            $total -= $depositAmount;
//        }
        //return $total ? round($total, 2) : 0;
        return $studentFinance ? round($studentFinance->kvartlis_jami, 2) : 0;
    }

    public function currentTotalPrice($finalDebt = null)
    {
        //$year = (now()->month >= 1 && now()->month <= 9) ? $this->learn_year - 1 : $this->learn_year;
        $qrter = Finance::find($this->financeId)->Qrter; //TODO: ამის ამოღება რა საჭიროა? როდესაც ინდივიდუალური გრაფიკის გაკეთებას ვცდილობ მიერორებს რადგან ფაინანს აიდი არ მიეწოდება
        $studentFinance = Finance::whereSaswavloWeli($this->learn_year)
            ->wherePiradiNom($this->getStudentIdentityNumber())
            ->where('ProgramID', $this->getStudentProgramID())
            ->where('Qrter', '<=', $qrter)
            ->orderByDesc('id')->get();
        $contractMoney = 0;
        $additionalSubjects = 0;
        $ratingDiscount = 0;
        $grantStatusDiscount = 0;
        $extraDiscount = 0;
        $academicStatusDiscount = 0;
        $stateDiscount = 0;
        $cityHallDiscount = 0;
        $grantDiscount = 0;
        $depositAmount = 0;
        foreach ($studentFinance as $item) {
            $contractMoney += $item['kontraqtis_tanxa'];
            $additionalSubjects += $item['dam_sagnebi'];
            $ratingDiscount += $item['sareitingo_fasdakleba'];
            $grantStatusDiscount += $item['grantianis_fasdakleba'];
            $extraDiscount += $item['extra'];
            $academicStatusDiscount += $item['akademiuris_tanxa1'];
            $stateDiscount += $item['sax_daxmareba'];
            $cityHallDiscount += $item['meriis_daxmareba'];
            $grantDiscount += $item['sax_granti'];
            $depositAmount += $item['charicxuli_studenti'];
        }

        if (isset($studentFinance)) {
            $total = $contractMoney
                + $additionalSubjects
                - $academicStatusDiscount
                - $grantDiscount
                - $ratingDiscount
                - $grantStatusDiscount
                - $extraDiscount
                - $stateDiscount
                - $cityHallDiscount;
//            $balanceOfTheQuarter = Finance::whereSaswavloWeli($year)
//                ->wherePiradiNom($this->getStudentIdentityNumber())
//                ->whereQrter(1)->first();
//            $total += $balanceOfTheQuarter->kvartlis_nashti;
        }
        if ($finalDebt == 1) {
            $total -= $depositAmount;
        }
        return $total ? round($total, 2)
            :
            0;
    }

    public function currentStudentPrice(): int
    {
        $year = (now()->month >= 1 && now()->month <= 9) ? $this->learn_year - 1 : $this->learn_year;
        $identityNumber = $this->getStudentIdentityNumber();
        $studentFinance = Finance::whereSaswavloWeli($this->learn_year)
            ->wherePiradiNom($identityNumber)
            ->orderByDesc('id')->first();
        return $this->currentTotalPrice()
            - $studentFinance->sax_daxmareba
            - $studentFinance->meriis_daxmareba;
    }

    public function getQuarter($year, $month, $day): int
    {
        if (Carbon::createFromDate($year, $month, $day)->isBetween(
            Carbon::createFromDate($year, Finance::QUARTERS['first'][0], 1)->subDay(),
            Carbon::createFromDate($year, Finance::QUARTERS['first'][1], 30)
        )) {
            return 1;
        } else if (
            Carbon::createFromDate($year, $month, $day)->isBetween(
                Carbon::createFromDate($year - 1, Finance::QUARTERS['second'][0], 1)->subDay(),
                Carbon::createFromDate($year, Finance::QUARTERS['second'][1], 1)->subDay()
            ) ||
            Carbon::createFromDate($year, $month, $day)->isBetween(
                Carbon::createFromDate($year, Finance::QUARTERS['second'][0], 1)->subDay(),
                Carbon::createFromDate($year + 1, Finance::QUARTERS['second'][1], 1)->subDay()
            )
        ) {
            return 2;
        } else if (
            Carbon::createFromDate($year, $month, $day)->isBetween(
                Carbon::createFromDate($year, Finance::QUARTERS['third'][0], 1)->subDay(),
                Carbon::createFromDate($year, Finance::QUARTERS['third'][1], 1)->subDay()
            )
        ) {
            return 3;
        }

        return 4;
    }

    public function hasGraphic(): bool
    {
        return FinanceScheduler::whereUserId($this->userId)
            ->whereYear('created_at', '=', $this->learn_year)
            ->whereIsActive(1)->exists();
    }

    public function quarterDebt(): int
    {
        $dateFrom = date(date('Y') . '-09-01');
        $dateTo = now()->format('Y-m');
        $finance = Finance::find($this->financeId);
        if ($this->hasGraphic()) {
            $year = $finance->weli;
            $financeSchedulerId = FinanceScheduler::whereIsActive(1)
                ->whereYear('created_at', '=', $this->learn_year)
                ->whereUserId($this->userId)->first()->id;
            [$dateFrom, $dateTo] = match ($finance->Qrter) {
                1 => ["01/09/$year", "31/09/$year"],
                2 => ["01/10/$year", "31/11/$year"],
                3 => ["01/12/$year", "28/02/$year+1"],
                4 => ["01/03/$year+1", "01/06/$year+1"]
            };
            $financeCalendarDebt = FinanceCalendar::whereFinanceSchedulerId($financeSchedulerId)
                ->where('start_date', '>=', $dateFrom)
                ->where('end_date', '<=', $dateTo)
                ->sum('amount');
            $payed = FinanceLog::whereBetween('tarigi', [$dateFrom, $dateTo])->sum('tanxa');
            return ($financeCalendarDebt - $payed) * -1;
        }
        $student = Finance::find($this->financeId);
        return $student->kvartlis_jami;
    }

    public
    function oldDebt(): int
    {
        $finance = Finance::find($this->financeId);
        return $finance ? $finance->kvartlis_nashti : 0;
    }

    public
    function debt(): int
    {
        $student = Student::whereUserId($this->userId)->first();
        $finance = Finance::where('piradi_nom', $student->personal_id ?? 0)->where('ProgramID', $student->program_id ?? 0)->orderByDesc('id')->first();
        if ($this->hasGraphic()) {
            $year = $finance->weli;
            $semester = $finance->Qrter <= 2 ? 1 : 2;
            $dates = $semester === 1
                ? ["01/09/$year", "31/12/$year"]
                : ["01/03/$year", "31/08/$year"];
            $financeSchedulerId = FinanceScheduler::whereUserId($this->userId)
                ->whereIsActive(1)->first()->id;
            $scheduledAmount = FinanceCalendar::whereFinanceSchedulerId($financeSchedulerId)
                ->where('start_date', '>=', $dates[0])
                ->where('end_date', '<=', $dates[1])
                ->sum('amount');
            $payedAmount = FinanceLog::whereBetween('tarigi', $dates)->sum('tanxa');
            return $payedAmount - $scheduledAmount;
        }
        return $finance?->kvartlis_jami ?? 0;
    }

    public
    function payments($personalId)
    {
        $financeLogs = FinanceLog::wherePiradiNom($personalId);
        if (request()->has('periods')) {
            [$startDate, $endDate] = explode(',', request()->periods);
            [$startDateDay, $startDateMonth, $startDateYear] = explode('/', $startDate);
            [$endDateDay, $endDateMonth, $endDateYear] = explode('/', $endDate);
            $financeLogs = $financeLogs->whereBetween('tarigi', [
                Carbon::createFromDate($startDateYear, $startDateMonth, $startDateDay)->format('Y-m-d H:i:s'),
                Carbon::createFromDate($endDateYear, $endDateMonth, $endDateDay)->format('Y-m-d H:i:s'),
            ]);
        }
        return $financeLogs->get();
    }

    public
    function totalPayed(): int
    {
        if (isset(request()->periods)) {
            [$fromDate, $toDate] = explode(',', request()->periods);
            return FinanceLog::whereBetween('tarigi', [$fromDate, $toDate])->sum('tanxa');
        }
        return FinanceLog::sum('tanxa');
    }

    public
    function getCurrentFinanceData()
    {
        $studentProgramId = Student::whereUserId($this->userId)
            ->first()->program_id ?? 0;
        $finance = Finance::find($this->financeId);
        $financeYear = $finance->saswavlo_weli;
        $financeIdentityNum = $finance->piradi_nom;
        $financeData = Finance::wherePiradiNom($financeIdentityNum)
            ->where('ProgramID', $studentProgramId)
            ->where('Qrter', '<=', $finance->Qrter)
            ->whereSaswavloWeli($financeYear);
        return $financeData;
    }

    public
    function currentYearDebt()
    {
        $financeData = $this->getCurrentFinanceData();
        return ($financeData->sum('kontraqtis_tanxa') + $financeData->sum('dam_sagnebi'));
    }

    public
    function currentYearDebtWithGrant()
    {
        $financeData = $this->getCurrentFinanceData();
        return ($financeData->sum('kontraqtis_tanxa') + $financeData->sum('dam_sagnebi'))
            - $financeData->sum('sareitingo_fasdakleba')
            - $financeData->sum('grantianis_fasdakleba')
            - $financeData->sum('extra')
            - $financeData->sum('akademiuris_tanxa1')
            - $financeData->sum('sax_daxmareba')
            - $financeData->sum('meriis_daxmareba');
    }

    public
    function currentYearStudentDebt()
    {
        $financeData = $this->getCurrentFinanceData();
        return ($financeData->sum('kontraqtis_tanxa') + $financeData->sum('dam_sagnebi'))
            - $financeData->sum('sareitingo_fasdakleba')
            - $financeData->sum('grantianis_fasdakleba')
            - $financeData->sum('extra')
            - $financeData->sum('sax_granti')
            - $financeData->sum('sax_daxmareba')
            - $financeData->sum('meriis_daxmareba')
            - $financeData->sum('akademiuris_tanxa1');
    }

    public
    function getSumByColumn($column)
    {
        return $this->getCurrentFinanceData()->sum($column);
    }

    public function getAdditionalData(): array
    {
        $programFilter = (new ProgramFilter());
        $schoolFilter = (new SchoolFilter());
        return [
            'programs' => Program::filter($programFilter)->pluck('name_ka', 'id'),
            'schools' => School::filter($schoolFilter)->pluck('name_ka', 'id'),
            'status' => StudentStatusList::pluck('name_ka', 'id'),
//            'totalPayed' => self::totalPayed(),
//            'ratingSale' => $this->ratingSale,
//            'extra' => $this->extra,
//            'contractMoney' => $this->contractMoney,
//            'academicMoney' => $this->academicMoney,
//            'oldDebt' => $this->oldDebt,
//            'quarterDebt' => $this->quarterDebt,
//            'debt' => $this->debt,
//            'grant' => $this->grant,
//            'otherHelp' => $this->otherHelp,
//            'socialHelp' => $this->socialHelp,
//            'totalPriceCurrent' => $this->totalPriceCurrent,
//            'studentPaid' => $this->studentPaid,
//            'currentYearStudentDebt' => $this->currentYearStudentDebt,
//            'currentYearDebtWithGrant' => $this->currentYearDebtWithGrant
        ];
    }

//    public
//    function getAdditionalData(): array
//    {
//        $programFilter = (new ProgramFilter());
//        $schoolFilter = (new SchoolFilter());
//        return [
//            'programs' => Program::filter($programFilter)->pluck('name_ka', 'id'),
//            'schools' => School::filter($schoolFilter)->pluck('name_ka', 'id'),
//            'status' => StudentStatusList::pluck('name_ka', 'id'),
//            'totalPayed' => self::totalPayed(),
//            'ratingSale' => Cache::get('ratingSale'),
//            'extra' => Cache::get('extra'),
//            'contractMoney' => Cache::get('contractMoney'),
//            'academicMoney' => Cache::get('academicMoney'),
//            'oldDebt' => Cache::get('oldDebt'),
//            'quarterDebt' => Cache::get('quarterDebt'),
//            'debt' => Cache::get('debt'),
//            'grant' => Cache::get('grant'),
//            'otherHelp' => Cache::get('otherHelp'),
//            'socialHelp' => Cache::get('socialHelp'),
//            'totalPriceCurrent' => Cache::get('totalPriceCurrent'),
//            'studentPaid' => Cache::get('studentPaid'),
//            'currentYearStudentDebt' => Cache::get('currentYearStudentDebt'),
//            'currentYearDebtWithGrant' => Cache::get('currentYearDebtWithGrant')
//            //'periods' => Setting::where('key', 'finance_start_learn_year')->pluck('value')->first() . ',' . Carbon::now()->format('d/m/Y')
//        ];
//    }

    public function setAdditionalData(): void
    {
        $this->ratingSale = 0;
        $this->extra = 0;
        $this->contractMoney = 0;
        $this->academicMoney = 0;
        $this->oldDebt = 0;
        $this->quarterDebt = 0;
        $this->debt = 0;
        $this->grant = 0;
        $this->otherHelp = 0;
        $this->socialHelp = 0;
        $this->totalPriceCurrent = 0;
        $this->studentPaid = 0;
        $this->currentYearStudentDebt = 0;
        $this->currentYearDebtWithGrant = 0;
    }

//    public
//    function setAdditionalData(): void
//    {
//        Cache::set('ratingSale', 0);
//        Cache::set('extra', 0);
//        Cache::set('contractMoney', 0);
//        Cache::set('academicMoney', 0);
//        Cache::set('oldDebt', 0);
//        Cache::set('quarterDebt', 0);
//        Cache::set('currentYearDebtWithGrant', 0);
//        Cache::set('debt', 0);
//        Cache::set('grant', 0);
//        Cache::set('otherHelp', 0);
//        Cache::set('socialHelp', 0);
//        Cache::set('totalPriceCurrent', 0);
//        Cache::set('studentPaid', 0);
//        Cache::set('currentYearStudentDebt', 0);
//        Cache::set('currentYearDebtWithGrant', 0);
//    }

    public function getFinancesData($finances)
    {
        return $finances->distinct('piradi_nom')->get()->map(function ($item) {
            $student = Student::wherePersonalId($item->piradi_nom)->first();
            $financeService = new self($student->user_id ?? 0, $item->id);
            $extra = $item->Qrter > 2
                ? $item->dam_sagnebi + Finance::where('piradi_nom', '=', $item->piradi_nom)
                    ->where('saswavlo_weli', '=', $item->saswavlo_weli)
                    ->where('Qrter', '=', 3)->first()->extra : $item->dam_sagnebi;

            $data = $this->calculateFinancesData($financeService, $item, $student, $extra);
            return $data;
        });
    }

    private function calculateFinancesData($financeService, $item, $student, $extra): array
    {
        $data = [
            'id' => $item->id,
            'quarter' => $item->Qrter,
            'ratingSale' => number_format($financeService->getSumByColumn('sareitingo_fasdakleba') + $financeService->getSumByColumn('grantianis_fasdakleba'), 2),
            'extra' => number_format($financeService->getSumByColumn('extra'), 2),
            'academicMoney' => number_format($financeService->getSumByColumn('akademiuris_tanxa1'), 2),
            'firstName' => $student->name ?? '',
            'lastName' => $student->surname ?? '',
            'personalId' => $item->piradi_nom,
            'contractMoney' => number_format($financeService->getSumByColumn('kontraqtis_tanxa'), 2),
            'extraSubjects' => $extra,
            'has_graphic' => $financeService->hasGraphic(),
            'oldDebt' => number_format($financeService->oldDebt(), 2),
            'currentYearDebtWithGrant' => number_format($financeService->currentYearDebt(), 2),
            'debt' => number_format($financeService->debt(), 2),
            'quarterDebt' => number_format($financeService->quarterDebt(), 2),
            'grant' => number_format($financeService->getSumByColumn('sax_granti'), 2),
            'socialHelp' => number_format($financeService->getSumByColumn('sax_daxmareba'), 2),
            'totalPriceCurrent' => number_format($financeService->currentTotalPrice(1), 2),
            'studentPaid' => number_format($financeService->getSumByColumn('charicxuli_studenti'), 2),
            'currentYearStudentDebt' => number_format($financeService->currentYearStudentDebt(), 2)
        ];

        return $data;
    }

//    public
//    function getFinancesData($finances)
//    {
//        return $finances->distinct('piradi_nom')->get()->map(function ($item) {
//            $student = Student::wherePersonalId($item->piradi_nom)->first();
//            $financeService = new self($student->user_id ?? 0, $item->id);
//            $extra = $item->Qrter > 2
//                ? $item->dam_sagnebi + Finance::where('piradi_nom', '=', $item->piradi_nom)
//                    ->where('saswavlo_weli', '=', $item->saswavlo_weli)
//                    ->where('Qrter', '=', 3)->first()->extra : $item->dam_sagnebi;
//            Cache::increment('ratingSale', (float)($item->sareitingo_fasdakleba + $item->grantianis_fasdakleba));
//            Cache::increment('extra', (float)$financeService->getSumByColumn('extra'));
//            Cache::increment('contractMoney', (float)($item->kontraqtis_tanxa));
//            Cache::increment('academicMoney', (float)$item->akademiuris_tanxa1);
//            Cache::increment('oldDebt', (float)$financeService->oldDebt());
//            Cache::increment('quarterDebt', $financeService->quarterDebt());
//            Cache::increment('currentYearDebtWithGrant', $financeService->currentYearDebt());
//            Cache::increment('debt', (float)$financeService->debt());
//            Cache::increment('grant', (float)($item->sax_granti * $item->Qrter));
//            Cache::increment('otherHelp', (float)$financeService->getSumByColumn('meriis_daxmareba'));
//            Cache::increment('socialHelp', (float)$financeService->getSumByColumn('sax_daxmareba'));
//            //'payments' => $financeService->payments(),
//            Cache::increment('totalPriceCurrent', (float)$financeService->currentTotalPrice());
//            Cache::increment('studentPaid', (float)$financeService->getSumByColumn('charicxuli_studenti'));
//            Cache::increment('currentYearStudentDebt', (float)$financeService->currentYearDebtWithGrant());
//            Cache::increment('currentYearDebtWithGrant', (float)$financeService->currentYearDebt());
//            return [
//                'id' => $item->id,
//                'quarter' => $item->Qrter,
//                'ratingSale' => number_format($financeService->getSumByColumn('sareitingo_fasdakleba') + $financeService->getSumByColumn('grantianis_fasdakleba'), 2),
//                'extra' => number_format($financeService->getSumByColumn('extra'), 2),
//                'academicMoney' => number_format($financeService->getSumByColumn('akademiuris_tanxa1'), 2),
//                'firstName' => $student->name ?? '',
//                'lastName' => $student->surname ?? '',
//                'personalId' => $item->piradi_nom,
//                'contractMoney' => number_format($financeService->getSumByColumn('kontraqtis_tanxa'), 2),
//                'extraSubjects' => $extra,
//                'has_graphic' => $financeService->hasGraphic(),
//                'oldDebt' => number_format($financeService->oldDebt(), 2), //წინა კვარტლიდან გადმოყოლებული ვალი
//                'currentYearDebtWithGrant' => number_format($financeService->currentYearDebt(), 2), //სტუდენტს რამდენი აქვს გადასახდელი წლიურად
//                'debt' => number_format($financeService->debt(), 2),
//                'quarterDebt' => number_format($financeService->quarterDebt(), 2), //ამჟამინდელი დავალიანება
//                'grant' => number_format($financeService->getSumByColumn('sax_granti'), 2),
//                'otherHelp' => number_format($financeService->getSumByColumn('meriis_daxmareba'), 2),
//                'socialHelp' => number_format($financeService->getSumByColumn('sax_daxmareba'), 2),
//                //'payments' => $financeService->payments(),
//                'totalPriceCurrent' => number_format($financeService->currentTotalPrice(1), 2),
//                'studentPaid' => number_format($financeService->getSumByColumn('charicxuli_studenti'), 2),
//                'currentYearStudentDebt' => number_format($financeService->currentYearStudentDebt(), 2)
//            ];
//        });
//    }

    public function studentGeneralInfo(): array
    {
        $studentIdentityNumber = self::getStudentIdentityNumber();
        $studentFinances = Finance::where('piradi_nom', $studentIdentityNumber)->where('saswavlo_weli', $this->learn_year)->where('ProgramID', $this->getStudentProgramID());
        if ($studentFinances->exists()) {
            $debt = self::getFinancesByQuarter($this->getQuarter($this->learn_year, now()->month, now()->day))->kvartlis_jami;
            //$debt = $studentFinances->where('Qrter', '<=', $this->getQuarter($this->learn_year, now()->month, now()->day))->sum('kvartlis_jami');

            $studentYearFinances = Finance::where('piradi_nom', $studentIdentityNumber)->where('saswavlo_weli', $this->learn_year)->where('ProgramID', $this->getStudentProgramID())->get();

            $totalPrice = $studentYearFinances->sum('kontraqtis_tanxa') + $studentYearFinances->sum('dam_sagnebi') + $studentYearFinances->where('Qrter', '<=', 1)->sum('kvartlis_nashti');
            return [
                'code' => 200,
                'data' => [
                    'contractMoney' => $studentYearFinances->sum('kontraqtis_tanxa'), //კონტრაქტის თანხა
                    'grant' => $studentYearFinances->sum('sax_granti') + $studentYearFinances->sum('sax_daxmareba') + $studentYearFinances->sum('meriis_daxmareba'), //სახელწმიფო გრანტი და სხვა ფასდაკლებები
                    'scholarship' => $studentYearFinances->sum('sareitingo_fasdakleba'), //სტიპენდია
                    'sale' => $studentYearFinances->sum('sareitingo_fasdakleba') + $studentYearFinances->sum('extra') + $studentYearFinances->sum('grantianis_fasdakleba'), //ჯამურად ფასდაკლება
                    'additionalSubjects' => $studentYearFinances->sum('dam_sagnebi'), //დამატებითი საგნების გადასახადი
                    'currentDebt' => $debt ?? 0, //მიმდინარე დავალიანება
                    'prePaid' => $debt < 0 ? $debt * -1 : 0, //ავანსად გადახდილი თანხა
                    'totalPrice' => $totalPrice, //ჯამურად გადასახდელი თანხა, აქ გათვალისწინებულია წინა წლის დავალიანება და დამატებითი საგნების საფასური
                    'lastYearDebt' => $studentYearFinances->where('Qrter', '<=', 1)->sum('kvartlis_nashti'), //წინა წლის დავალიანება
                ]
            ];
        }
        return [
            'code' => 404,
            'message' => 'Student payment general info not found'
        ];
    }

    public function commitments($id = null): array
    {
        if($id){
            $user_id = Student::whereId($id)->first()->user_id;
        } else {
            $user_id = auth()->id();
        }
        $startLearnYear = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();
        $financeScheduler = FinanceScheduler::whereUserId($user_id)->where('is_active', 1);

        if ($financeScheduler !== null && $financeScheduler->exists()) {
            $mappedSchedulerData = FinanceCalendar::select('id', 'finance_scheduler_id', 'start_date', 'end_date', 'amount')
                ->whereFinanceSchedulerId($financeScheduler->first()->id)->get();

            $mappedSchedulerData = $mappedSchedulerData->map(function ($item,$key) use ($mappedSchedulerData, $startLearnYear){
                $startLearnYear = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();

                $startDate = $key == 0
                    ? $startLearnYear
                    : $mappedSchedulerData[$key-1]->start_date;

                $paidMoney = FinanceLog::wherePiradiNom(self::getStudentIdentityNumber())
                    ->whereBetween('tarigi', [
                        Carbon::createFromFormat('d/m/Y', $startDate),
                        Carbon::createFromFormat('d/m/Y', $item->start_date)
                    ])
                    ->sum('tanxa');
                    return [
                        'start_date' => $item->start_date,
                        'end_date' => $item->start_date,
                        'paymentMoney' => $item['amount'],
                        'paidMoney' => $paidMoney,
                        'amountToPay' => round((float) $item['amount'] - $paidMoney, 2)
                    ];
                })->toArray();
            return [
                'code' => 200,
                'scheduler' => true,
                'data' => $mappedSchedulerData
            ];
        }
        $currentYear = $this->learn_year;
        $nextYear = $currentYear + 1;

        $firstQuarter = self::getFinancesByQuarter(1);
        $secondQuarter = self::getFinancesByQuarter(2);
        $thirdQuarter = self::getFinancesByQuarter(3);
        $fourthQuarter = self::getFinancesByQuarter(4);
//გადასახდელი თანხა კვარტლის მიხედვით
        $paymentMoneyFirst=$firstQuarter?->kontraqtis_tanxa + $firstQuarter?->dam_sagnebi + $firstQuarter?->kvartlis_nashti ?? 0;
        $paymentMoneySecond=$secondQuarter?->kontraqtis_tanxa + $secondQuarter?->kvartlis_nashti ?? 0;
        $paymentMoneyThird=$thirdQuarter?->kontraqtis_tanxa  + $thirdQuarter?->dam_sagnebi + $thirdQuarter?->kvartlis_nashti ?? 0;
        $paymentMoneyFourth=$fourthQuarter?->kontraqtis_tanxa + $fourthQuarter?->kvartlis_nashti ?? 0;


//გადახდილი თანხა კვარტლის მიხედვით
        $paidMoneyFirst=$firstQuarter?->charicxuli_studenti ?? 0;
        $paidMoneySecond=$secondQuarter?->charicxuli_studenti ?? 0;
        $paidMoneyThird=$thirdQuarter?->charicxuli_studenti ?? 0;
        $paidMoneyFourth=$fourthQuarter?->charicxuli_studenti ?? 0;
//დარჩენილი გადასახდელი თანხა კვარტლის მიხედვით
        $amountToPayFirst=$firstQuarter?->kvartlis_jami ?? 0;
        $amountToPaySecond=$secondQuarter?->kvartlis_jami ?? 0;
        $amountToPayThird=$thirdQuarter?->kvartlis_jami ?? 0;
        $amountToPayFourth=$fourthQuarter?->kvartlis_jami ?? 0;


        return [
            'code' => 200,
            'scheduler' => false,
            'data' => [
                [
                    'start_date' => "01/09/{$currentYear}",
                    'end_date' => "01/10/{$currentYear}",
                    'paymentMoney' => $paymentMoneyFirst,
                    'paidMoney' => $paidMoneyFirst,
                    'amountToPay' => max($amountToPayFirst, 0),
                ],
                [
                    'start_date' => "02/10/{$currentYear}",
                    'end_date' => "01/12/{$currentYear}",
                    'paymentMoney' => $paymentMoneySecond,
                    'paidMoney' => $paidMoneySecond,
                    'amountToPay' => max($amountToPaySecond, 0)
                ],
                [
                    'start_date' => "02/12/{$currentYear}",
                    'end_date' => "01/03/{$nextYear}",
                    'paymentMoney' => $paymentMoneyThird,
                    'paidMoney' => $paidMoneyThird,
                    'amountToPay' => max($amountToPayThird, 0),
                ],
                [
                    'start_date' => "02/03/{$nextYear}",
                    'end_date' => "01/06/{$nextYear}",
                    'paymentMoney' => $paymentMoneyFourth,
                    'paidMoney' => $paidMoneyFourth,
                    'amountToPay' => max($amountToPayFourth, 0),
                ],
            ]
        ];
    }

    public function getFinancesByQuarter(int $quarter): ?Finance
    {
        return Finance::where('piradi_nom', self::getStudentIdentityNumber())
            ->where('saswavlo_weli', $this->learn_year)
            ->where('ProgramID', $this->getStudentProgramID())
            ->where('Qrter', $quarter)
            ->first();
    }

    public function studentCurrentDebtForAuth(): string
    {
        $studentIdentityNumber = self::getStudentIdentityNumber();
        $studentFinances = Finance::with('student')
            ->where('piradi_nom', $studentIdentityNumber)
            ->where('ProgramID', $this->getStudentProgramID())
            ->where('saswavlo_weli', $this->learn_year);

        if ($studentFinances->exists())
        {
            $studentFinances = $studentFinances
                ->where('Qrter', '<=', $this->getQuarter($this->learn_year, now()->month, now()->day))
                ->get()
            ;

            $student = $studentFinances->first()?->student;
            if ($student && $student->financeScheduler)
            {
                $financeCalendarDebt = FinanceCalendar::whereFinanceSchedulerId($student->financeScheduler->id)
                    ->where('end_date', '<=', now())
                    ->sum('amount')
                ;
                $financeStartLearnYear = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();

                $payed = FinanceLog::where('piradi_nom', $student->personal_id)
                    ->whereBetween('tarigi', [Carbon::createFromFormat('d/m/Y', $financeStartLearnYear), now()])
                    ->sum('tanxa');

                $debt = $financeCalendarDebt - $payed;
            }else{
                $debt = self::getFinancesByQuarter($this->getQuarter($this->learn_year, now()->month, now()->day))->kvartlis_jami ?? 0;
//                $debt = $studentFinances
//                    ->where('Qrter', '<=', $this->getQuarter($this->learn_year, now()->month, now()->day))
//                    ->sum('kvartlis_jami');
            }

            return $debt;
        }
        return 0;
    }
}
