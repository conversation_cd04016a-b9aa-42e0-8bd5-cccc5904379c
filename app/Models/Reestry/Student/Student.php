<?php

namespace App\Models\Reestry\Student;

use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\LectureStudent;
use App\Models\Finance\FinanceScheduler;
use App\Models\Lectures\Lecture;
use App\Models\Own;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Scopes\AccessibleScope;
use App\Models\Setting;
use App\Models\SystemLog;
use App\Models\User\User;
use App\Traits\HasFilters;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Student
 *
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $surname
 * @property string|null $personal_id
 * @property string|null $personal_id_number
 * @property int $sex 0=მდედრობითი;1=მამრობითი
 * @property string|null $address
 * @property string|null $citizenship
 * @property string|null $phone
 * @property string|null $birthday
 * @property string|null $enrollment_date
 * @property string|null $enrollment_order
 * @property string $email
 * @property int|null $status_id
 * @property int $mobility 0=არა;1=კი
 * @property int|null $basics_of_enrollement_id
 * @property string|null $photo
 * @property string|null $notes
 * @property string|null $cv_file_name
 * @property string|null $diploma_file_name
 * @property string|null $transcript_file_name
 * @property string|null $motivation_article_file_name
 * @property int $diploma_taken 0=არა;1=კი
 * @property string|null $diploma_taken_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Reestry\Student\StudentBasicsOfEnrollment|null $basics_of_enrollment
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentGroupHistory[] $group_history
 * @property-read int|null $group_history_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentMobilityStatusHistory[] $mobility_status
 * @property-read int|null $mobility_status_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentProgramHistory[] $program_history
 * @property-read int|null $program_history_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentStatusHistory[] $status_history
 * @property-read int|null $status_history_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentProfileFileList[] $student_profile_file_list
 * @property-read int|null $student_profile_file_list_count
 * @property-read \App\Models\User\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Student newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Student newQuery()
 * @method static \Illuminate\Database\Query\Builder|Student onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Student query()
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereBasicsOfEnrollementId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereBirthday($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereCitizenship($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereCvFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereDiplomaFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereDiplomaTaken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereDiplomaTakenDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereEnrollmentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereEnrollmentOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereMobility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereMotivationArticleFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student wherePersonalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student wherePersonalIdNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student wherePhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereSex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereSurname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereTranscriptFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|Student withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Student withoutTrashed()
 * @property-read \App\Models\Reestry\Student\StudentStatusList|null $status
 * @property string|null $bio
 * @property int $school_id
 * @property int $program_id
 * @property int|null $group_id
 * @property int $learn_year_id
 * @property int $course
 * @property-read \App\Models\Reestry\Student\StudentBasicsOfEnrollment|null $basicOfEnrollment
 * @property-read LearnYear|null $learnYear
 * @property-read \Illuminate\Database\Eloquent\Collection|LearnYear[] $learnYears
 * @property-read int|null $learn_years_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Lecture[] $lectures
 * @property-read int|null $lectures_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentMobilityStatusHistory[] $mobilityStatuses
 * @property-read int|null $mobility_statuses_count
 * @property-read Program|null $program
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentProgramHistory[] $programHistories
 * @property-read int|null $program_histories_count
 * @property-read School|null $school
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentStatusHistory[] $statusHistories
 * @property-read int|null $status_histories_count
 * @property-read \App\Models\Reestry\Student\StudentGroup|null $studentGroup
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentGroup[] $studentGroups
 * @property-read int|null $student_groups_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentProfileFileList[] $studentProfileFileLists
 * @property-read int|null $student_profile_file_lists_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentSyllabusHistory[] $syllabi
 * @property-read int|null $syllabi_count
 * @method static \Database\Factories\Reestry\Student\StudentFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Student filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereCourse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereLearnYearId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereSchoolId($value)
 * @property string|null $name_en
 * @property string|null $surname_en
 * @property string|null $parent_phone
 * @property int|null $applicant_id
 * @property string|null $applicant_type
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereApplicantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereApplicantType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereParentPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereSurnameEn($value)
 * @property int $lmb_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, CurriculumLecture> $curriculumLectures
 * @property-read int|null $curriculum_lectures_count
 * @property-read FinanceScheduler|null $financeScheduler
 * @method static \Illuminate\Database\Eloquent\Builder|Student whereLmbId($value)
 * @mixin \Eloquent
 */
class Student extends Model
{
    use SoftDeletes, HasFactory, HasFilters;

    protected $fillable = [
        'user_id',
        'name',
        'name_en',
        'surname',
        'surname_en',
        'personal_id',
        'personal_id_number',
        'sex',
        'address',
        'citizenship',
        'phone',
        'parent_phone',
        'birthday',
        'enrollment_date',
        'enrollment_order',
        'email',
        'school_id',
        'program_id',
        'group_id',
        'status_id',
        'learn_year_id',
        'mobility',
        'basics_of_enrollement_id',
        'photo',
        'notes',
        'cv_file_name',
        'diploma_file_name',
        'transcript_file_name',
        'motivation_article_file_name',
        'diploma_taken',
        'diploma_taken_date',
        'bio',
        'course',
        'created_at',
        'updated_at',
        'deleted_at',
        'applicant_id',
        'applicant_type',
        'lmb_id',
        'gpa',
        'diploma_number',
        'minor_id',
    ];


    protected $casts = [
        'birthday' => 'date:d-m-Y',
        'enrollment_date' => 'date:d-m-Y',
        'diploma_taken_date' => 'date:d-m-Y',
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);

            if ($model->isDirty('group_id')) {
                StudentGroupHistory::query()->create([
                    'student_id' => $model->id,
                    'group_id' => $model->group_id,
                ]);
            }

            if ($model->isDirty('learn_year_id')) {
                StudentLearnYearHistory::query()->create([
                    'student_id' => $model->id,
                    'learn_year_id' => $model->learn_year_id,
                ]);
            }

            if ($model->isDirty('program_id')) {
                StudentProgramHistory::query()->create([
                    'student_id' => $model->id,
                    'program_id' => $model->program_id,
                ]);
            }
        });

        // Deleting event
        static::deleting(function ($model) {
            LectureStudent::query()->where('student_id', $model->id)->delete();
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function group_history(): HasMany
    {
        return $this->hasMany(StudentGroupHistory::class);
    }

    public function mobilityStatuses(): HasMany
    {
        return $this->hasMany(StudentMobilityStatusHistory::class);
    }

    public function programHistories(): HasMany
    {
        return $this->hasMany(StudentProgramHistory::class);
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(StudentStatusList::class, 'status_id', 'id');
    }

    public function statusHistories(): HasMany
    {
        return $this->hasMany(StudentStatusHistory::class);
    }

    public function basicOfEnrollment(): BelongsTo
    {
        return $this->belongsTo(
            StudentBasicsOfEnrollment::class,
            'basics_of_enrollment_id',
            'id'
        );
    }

    public function studentProfileFileLists(): HasMany
    {
        return $this->hasMany(StudentProfileFileList::class);
    }

    public function studentGroup(): BelongsTo
    {
        return $this->belongsTo(StudentGroup::class, 'group_id', 'id');
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function learnYear(): BelongsTo
    {
        return $this->belongsTo(LearnYear::class);
    }

    //    public function birthday(): Attribute
    //    {
    //        return Attribute::make(
    //            get: fn($value) => Carbon::parse($value),
    //            set: fn($value) => Carbon::createFromFormat('d/m/Y', $value)
    //        );
    //    }
    //
    //    public function enrollmentDate(): Attribute
    //    {
    //        return Attribute::make(
    //            get: fn($value) => Carbon::parse($value),
    //            set: fn($value) => Carbon::createFromFormat('d/m/Y', $value)
    //        );
    //    }
    //
    //    public function diplomaTakenDate(): Attribute
    //    {
    //        return Attribute::make(
    //            get: fn($value) => $value ? Carbon::parse($value) : null,
    //            set: fn($value) => $value ? Carbon::createFromFormat('d/m/Y', $value) : null
    //        );
    //    }

    public function diplomaTaken(): Attribute
    {
        return Attribute::make(
            set: fn($value) => (int)$value
        );
    }

    public function learnYears(): BelongsToMany
    {
        return $this->belongsToMany(
            LearnYear::class,
            'student_learn_year'
        );
    }

    public function studentGroups(): BelongsToMany
    {
        return $this->belongsToMany(StudentGroup::class);
    }

    public function syllabi()
    {
        return $this->hasMany(StudentSyllabusHistory::class, 'student_id', 'id');
    }

    public function lectures()
    {
        return $this->belongsToMany(Lecture::class, 'lecture_student');
    }

    public function fullName(): Attribute
    {
        return Attribute::make(
            get: fn() => "{$this->name} {$this->surname}"
        );
    }

    public function financeScheduler(): BelongsTo
    {
        $startDate = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();
        $endDate = Setting::where('key', 'finance_learn_year_manual')->pluck('value')->first();

        $startDateFormatted = Carbon::createFromFormat('d/m/Y', $startDate)->format('Y-m-d');
        $endDateFormatted = Carbon::createFromFormat('d/m/Y', $endDate)->format('Y-m-d');

        return $this->belongsTo(FinanceScheduler::class, 'user_id', 'user_id')
            ->where('is_active', '=', 1)
            ->whereBetween('created_at', [$startDateFormatted, $endDateFormatted]);
    }

    public function activeSchedule(): BelongsTo
    {
        return $this->belongsTo(FinanceScheduler::class, 'user_id', 'user_id')
            ->where('is_active', '=', 1)
        ;
    }


    public function curriculumLectures(): BelongsToMany
    {
        return $this->belongsToMany(CurriculumLecture::class, 'lecture_student', 'student_id', 'lecture_id', 'id', 'id');
    }
}
