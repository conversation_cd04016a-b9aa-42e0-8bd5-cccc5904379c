<?php

namespace App\Jobs;

use App\Exports\StudentFinanceExport;
use App\Mail\StudentFinanceExportMail;
use App\Models\Reestry\Administration\Administration;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class ExportStudentFinanceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $adminUserId;

    /**
     * Create a new job instance.
     *
     * @param int $adminUserId
     */
    public function __construct(int $adminUserId)
    {
        $this->adminUserId = $adminUserId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // Get administrator info
            $admin = Administration::where('user_id', $this->adminUserId)->first();
            
            if (!$admin) {
                throw new \Exception('Administrator not found');
            }

            // Generate filename with timestamp
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = "student_finance_export_{$timestamp}.xlsx";
            $filePath = "exports/{$filename}";

            // Create the export
            $export = new StudentFinanceExport();
            
            // Store the file
            Excel::store($export, $filePath, 'local');
            
            // Get the full path
            $fullPath = Storage::path($filePath);
            
            // Send email with attachment
            Mail::to($admin->email)->send(new StudentFinanceExportMail($fullPath, $filename, $admin->full_name));
            
            // Clean up the file after sending email
            Storage::delete($filePath);
            
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Student Finance Export Job failed: ' . $e->getMessage());
            
            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        // Log the failure
        \Log::error('Student Finance Export Job failed permanently: ' . $exception->getMessage());
        
        // Optionally send notification to admin about failure
        try {
            $admin = Administration::where('user_id', $this->adminUserId)->first();
            if ($admin) {
                // You could send a failure notification email here
                \Log::info("Export failed for admin: {$admin->email}");
            }
        } catch (\Exception $e) {
            \Log::error('Failed to notify admin about export failure: ' . $e->getMessage());
        }
    }
}
